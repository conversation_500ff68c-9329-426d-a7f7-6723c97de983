from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.output_parser import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from dotenv import load_dotenv
import streamlit as st
import os
load_dotenv() 
api_key  = os.getenv("api_key")

llm = ChatGoogleGenerativeAI(
    model="gemini-1.5-flash",
    google_api_key = api_key
)


print(llm.invoke("Hello Gemini").content)


# response = llm.invoke("Say hello from LangChain setup!")
# print(response.content)